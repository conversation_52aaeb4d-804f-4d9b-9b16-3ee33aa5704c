(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          // 获取当前段落位置的辅助函数
          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() === 'numberingLevel') {
            // 获取当前编号的详细信息
            var currentLevel = oNumberingLevel.GetLevelIndex();
            var oNumbering = oNumberingLevel.GetNumbering();
            console.log('当前段落信息:');
            console.log('  - 编号层级:', currentLevel);
            console.log('  - 编号对象:', oNumbering);

            // 查找上一个段落的编号信息
            console.log('查找上一个段落的编号信息...');
            var previousNumberingLevel = null;
            var foundPreviousIndex = -1;

            // 从当前段落位置向前查找上一个编号段落
            for (var i = currentPosition - 1; i >= 0; i--) {
              var prevParagraph = allParagraphs[i];
              var prevNumbering = prevParagraph.GetNumbering();

              if (prevNumbering && prevNumbering.GetClassType() === 'numberingLevel') {
                var prevLevel = prevNumbering.GetLevelIndex();
                var prevNumberingObj = prevNumbering.GetNumbering();

                console.log('上一段落信息:');
                console.log('  - 段落位置:', i);
                console.log('  - 段落内容:', prevParagraph.GetText().trim());
                console.log('  - 编号层级:', prevLevel);
                console.log('  - 编号对象:', prevNumberingObj);

                if (prevLevel == currentLevel) {
                  previousNumberingLevel = prevNumbering;
                  foundPreviousIndex = i;
                }
              }
            }

            return;

            if (previousNumberingLevel) {
              console.log('=== 应用继续编号 ===');

              // 获取上一个段落的编号对象
              var prevNumberingObj = previousNumberingLevel.GetNumbering();
              console.log(prevNumberingObj);

              // 使用相同的编号对象，但获取当前层级
              var continueLevel = prevNumberingObj.GetLevel(currentLevel);

              // 应用编号（这将自动继续编号序列）
              oParagraph.SetNumbering(continueLevel);
            }
            return 1;
          } else {
            console.log('=== 当前段落无编号信息！ ===');
            return -1;
          }
        } catch (error) {
          console.error('继续编号过程中发生错误:', error);
          return -1;
        }
      },
      false,
      true,
      function (returnValue) {
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
