(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          // 获取当前段落位置的辅助函数
          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() === 'numberingLevel') {
            var currentLevel = oNumberingLevel.GetLevelIndex();

            var previousNumberingLevel = null;

            var specifiedParagraph = allParagraphs[currentPosition - 1];
            var specifiedNumbering = specifiedParagraph.GetNumbering();
            if (specifiedNumbering && specifiedNumbering.GetClassType() === 'numberingLevel') {
              var specifiedLevel = specifiedNumbering.GetLevelIndex();
              if (specifiedLevel == currentLevel) previousNumberingLevel = specifiedNumbering;
            }

            if (previousNumberingLevel) {
              var specifiedNumberingObj = previousNumberingLevel.GetNumbering();
              var newPrevNumberingObj = specifiedNumberingObj.GetLevel(currentLevel);
              oParagraph.SetNumbering(newPrevNumberingObj);

              for (var j = currentPosition + 1; j < allParagraphs.length; j++) {
                var nextParagraph = allParagraphs[j];
                var nextNumbering = nextParagraph.GetNumbering();
                if (nextNumbering && nextNumbering.GetClassType() === 'numberingLevel') {
                  var nextLevel = nextNumbering.GetLevelIndex();
                  if (nextLevel < currentLevel) {
                    break;
                  }
                  if (nextLevel >= currentLevel) {
                    var newNextNumberingObj = specifiedNumberingObj.GetLevel(nextLevel);
                    nextParagraph.SetNumbering(newNextNumberingObj);
                  }
                }
              }
            }
            return 1;
          } else {
            console.log('当前段落无编号信息！');
            return -1;
          }
        } catch (error) {
          console.error('发生错误:', error);
          return -1;
        }
      },
      false,
      true,
      function () {
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function () {};
})(window, undefined);
