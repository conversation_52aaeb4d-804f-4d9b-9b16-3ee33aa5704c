(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          // 获取当前段落位置的辅助函数
          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();
          console.log('当前段落编号检查结果:', oNumberingLevel ? '有编号' : '无编号');

          if (oNumberingLevel && oNumberingLevel.GetClassType() === 'numberingLevel') {
            // 获取当前编号的详细信息
            var currentLevel = oNumberingLevel.GetLevelIndex();
            var oNumbering = oNumberingLevel.GetNumbering();
            console.log('当前编号层级:', currentLevel);
            console.log('当前编号对象:', oNumbering);

            // 直接基于当前段落的编号继续编号
            console.log('=== 基于当前段落编号继续编号 ===');
            console.log('当前段落文本:', oParagraph.GetText());

            // 获取当前编号对象的相同层级
            var continueLevel = oNumbering.GetLevel(currentLevel);
            console.log('获取当前编号的层级对象:', continueLevel);

            // 重新应用编号（这将继续当前的编号序列）
            oParagraph.SetNumbering(continueLevel);
            console.log('成功应用继续编号');
            console.log('当前段落将继续当前编号的序列');
            console.log('=== 继续编号处理完成 ===');
            return 1; // 成功状态
          } else {
            console.log('=== 当前段落无编号信息！ ===');
            return -1; // 无编号状态，将显示错误提示
          }
        } catch (error) {
          console.error('继续编号过程中发生错误:', error);
          return -1; // 错误状态
        }
      },
      false,
      true,
      function (returnValue) {
        // 根据callCommand的返回值显示不同的通知
        console.log('callCommand返回值:', returnValue);

        if (returnValue === -1) {
          // 当前段落无编号信息
          this.executeMethod('ShowNotification', ['当前段落无编号信息！']);
        } else if (returnValue === 1) {
          // 继续编号成功
          this.executeMethod('ShowNotification', ['继续编号已应用']);
        } else {
          // 其他情况
          this.executeMethod('ShowNotification', ['操作完成']);
        }

        // 关闭插件
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function () {};
})(window, undefined);
