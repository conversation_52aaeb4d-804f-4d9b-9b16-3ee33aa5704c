(function(window, undefined) {
    
    window.Asc.plugin.init = function() {
        // 继续编号插件初始化
        console.log("继续编号插件已初始化");
        
        // 自动执行继续编号功能
        this.continueNumbering();
    };

    // 继续编号功能 - 相当于"继续编号"
    window.Asc.plugin.continueNumbering = function() {
        console.log("执行继续编号");
        
        this.callCommand(function() {
            var oDocument = Api.GetDocument();
            var oParagraph = oDocument.GetCurrentParagraph();
            
            if (oParagraph) {
                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();
                
                if (oNumberingLevel) {
                    // 如果当前段落已有编号，保持当前编号样式
                    // 这里不需要特别操作，因为默认就是继续编号
                    console.log("当前段落已有编号，继续使用");
                } else {
                    // 如果当前段落没有编号，查找文档中最近使用的编号样式
                    var allParagraphs = oDocument.GetAllParagraphs();
                    var lastNumbering = null;
                    
                    // 从当前段落向前查找最近的编号段落
                    for (var i = allParagraphs.length - 1; i >= 0; i--) {
                        var para = allParagraphs[i];
                        var numLevel = para.GetNumbering();
                        if (numLevel) {
                            lastNumbering = numLevel;
                            break;
                        }
                    }
                    
                    if (lastNumbering) {
                        // 使用找到的编号样式
                        oParagraph.SetNumbering(lastNumbering);
                    } else {
                        // 如果没有找到编号样式，创建默认编号
                        var oNumbering = oDocument.CreateNumbering("numbered");
                        var oLevel = oNumbering.GetLevel(0);
                        oParagraph.SetNumbering(oLevel);
                    }
                }
            }
        }, true);
        
        // 显示成功消息并关闭插件
        this.executeMethod('ShowNotification', ['继续编号已应用']);
        this.executeMethod('ClosePlugin');
    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            // 主按钮被点击，执行继续编号
            this.continueNumbering();
        }
    };

})(window, undefined);
