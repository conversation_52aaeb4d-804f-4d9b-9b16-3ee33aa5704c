(function (window, undefined) {
  function continueNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          // 获取当前段落位置的辅助函数
          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          var allParagraphs = oDocument.GetAllParagraphs();
          var currentPosition = getCurrentParagraphPosition();

          if (currentPosition >= allParagraphs.length) {
            console.error('段落位置超出范围:', currentPosition, '总段落数:', allParagraphs.length);
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() === 'numberingLevel') {
            // 获取当前编号的详细信息
            var currentLevel = oNumberingLevel.GetLevelIndex();
            var oNumbering = oNumberingLevel.GetNumbering();
            console.log('当前段落信息:');
            console.log('  - 编号层级:', currentLevel);
            console.log('  - 编号对象:', oNumbering);

            // 查找上一个段落的编号信息
            var previousNumberingLevel = null;

            var prevParagraph = allParagraphs[currentPosition - 1];
            var prevNumbering = prevParagraph.GetNumbering();

            if (prevNumbering && prevNumbering.GetClassType() === 'numberingLevel') {
              var prevLevel = prevNumbering.GetLevelIndex();
              var prevNumberingObj = prevNumbering.GetNumbering();

              console.log('遍历段落信息:');
              console.log('  - 段落内容:', prevParagraph.GetText().trim());
              console.log('  - 编号层级:', prevLevel);
              console.log('  - 编号对象:', prevNumberingObj);

              if (prevLevel == currentLevel) previousNumberingLevel = prevNumbering;
            }

            if (previousNumberingLevel) {
              console.log('=== 应用继续编号 ===');

              // 获取上一个段落的编号对象
              var prevNumberingObj = previousNumberingLevel.GetNumbering();
              console.log(prevNumberingObj);

              // 使用相同的编号对象，但获取当前层级
              var continueLevel = prevNumberingObj.GetLevel(currentLevel);

              // 应用编号（这将自动继续编号序列）
              oParagraph.SetNumbering(continueLevel);
              console.log('成功应用继续编号');

              // 重新编号后面相同编号类型的段落
              console.log('=== 开始重新编号后续段落 ===');
              var subsequentParagraphs = [];

              // 找到当前段落后面相同编号类型的段落
              for (var j = currentPosition + 1; j < allParagraphs.length; j++) {
                var nextPara = allParagraphs[j];
                var nextNumbering = nextPara.GetNumbering();

                if (nextNumbering && nextNumbering.GetClassType() === 'numberingLevel') {
                  var nextLevel = nextNumbering.GetLevelIndex();

                  // 如果是相同层级的编号段落
                  if (nextLevel === currentLevel) {
                    subsequentParagraphs.push({
                      index: j,
                      paragraph: nextPara,
                      numbering: nextNumbering
                    });
                    console.log('找到后续编号段落，位置:', j, '文本:', nextPara.GetText().substring(0, 30) + '...');
                  }
                  // 如果遇到更高层级的编号，停止查找
                  else if (nextLevel < currentLevel) {
                    console.log('遇到更高层级编号，停止查找，位置:', j, '层级:', nextLevel);
                    break;
                  }
                }
              }

              console.log('找到需要重新编号的后续段落数:', subsequentParagraphs.length);

              // 创建新的编号对象确保后续段落重新开始编号
              if (subsequentParagraphs.length > 0) {
                console.log('创建新编号对象用于后续段落重新编号');

                // 检测编号类型
                var paragraphText = oParagraph.GetText();
                var numberingType = 'numbered'; // 默认

                if (paragraphText) {
                  var firstChar = paragraphText.trim().charAt(0);
                  if (firstChar === '•' || firstChar === '○' || firstChar === '■' ||
                      firstChar === '▪' || firstChar === '▫' || firstChar === '◦') {
                    numberingType = 'bulleted';
                    console.log('检测到项目符号类型');
                  } else {
                    console.log('检测到数字编号类型');
                  }
                }

                // 创建新的编号对象用于后续段落
                var oNewNumbering = oDocument.CreateNumbering(numberingType);
                var oNewLevel = oNewNumbering.GetLevel(currentLevel);
                console.log('创建新编号对象，类型:', numberingType, '层级:', currentLevel);

                // 为后续段落应用新的编号对象（这将重新开始编号）
                for (var k = 0; k < subsequentParagraphs.length; k++) {
                  var subPara = subsequentParagraphs[k];
                  try {
                    subPara.paragraph.SetNumbering(oNewLevel);
                    console.log('重新编号段落', subPara.index, '成功（使用新编号对象）');
                  } catch (subError) {
                    console.log('重新编号段落', subPara.index, '失败:', subError.message);
                  }
                }
              }

              console.log('=== 后续段落重新编号完成 ===');
            }
            return 1;
          } else {
            console.log('=== 当前段落无编号信息！ ===');
            return -1;
          }
        } catch (error) {
          console.error('继续编号过程中发生错误:', error);
          return -1;
        }
      },
      false,
      true,
      function () {
        this.executeMethod('ShowNotification', ['继续编号已应用']);
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    continueNumbering.call(this);
  };

  window.Asc.plugin.button = function () {};
})(window, undefined);
