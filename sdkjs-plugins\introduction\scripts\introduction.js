/**
 *
 * (c) Copyright Ascensio System SIA 2020
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Example insert text into editors (different implementations)

(function (window, undefined) {
  function setForewordHeading() {
    // 主搜索函数
    this.callCommand(
      function () {
        var titleText = '引言';
        // 获取当前文档
        var oDocument = Api.GetDocument();
        var allHeading = oDocument.GetAllHeadingParagraphs();
        for (var i = 0; i < allHeading.length; i++) {
          var oHeading = allHeading[i];
          var oText = oHeading.GetText();

          if (oText.trim() == titleText) {
            // 一定要用trim()方法去除前后空格
            return '-1';
          }
        }

        var oAllStyles = oDocument.GetAllStyles();
        var gStyle = null; //oDocument.GetDefaultStyle().GetTextPr();
        for (var i = 0; i < oAllStyles.length; i++) {
          var oStyle = oAllStyles[i];
          if (oStyle.GetName() == '标准文件_前言、引言标题') {
            gStyle = oStyle;
          }
        }

        const oParagraph = Api.CreateParagraph();
        oParagraph.SetStyle(gStyle);

        var startPos = 0;
        var endPos = titleText.length - 1;
        // 添加选择前的文本（无特殊间距）
        if (startPos > 0) {
          var oRunBefore = oParagraph.AddText(titleText.substring(0, startPos));
          var oRunBeforePr = oRunBefore.GetTextPr() || Api.CreateRunPr();
          oRunBeforePr.SetSpacing(0);
          oRunBefore.SetTextPr(oRunBeforePr);
        }

        // 添加选中的文本（带间距）
        var oRunSelected = oParagraph.AddText(titleText.substring(startPos, endPos));
        var oRunSelectedPr = oRunSelected.GetTextPr() || Api.CreateRunPr();
        oRunSelectedPr.SetSpacing(300); // 3pt间距
        oRunSelected.SetTextPr(oRunSelectedPr);

        // 添加选择后的文本（无特殊间距）
        if (endPos < titleText.length) {
          var oRunAfter = oParagraph.AddText(titleText.substring(endPos));
          var oRunAfterPr = oRunAfter.GetTextPr() || Api.CreateRunPr();
          oRunAfterPr.SetSpacing(0);
          oRunAfter.SetTextPr(oRunAfterPr);
        }

        // 查找目标标题位置
        var insertPosition = -1;
        let targetHeadingLevel = 0;
        let targetHeadingFound = false;
        var targetHeadingText = '前言';
        var levels = [
          {
            name: '标准文件_目录标题',
            level: '1',
          },
          {
            name: '标准文件_前言、引言标题',
            level: '1',
          },
          {
            name: '标准文件_正文标准名称',
            level: '1',
          },
          {
            name: '标准文件_章标题',
            level: '1',
          },
          {
            name: '标准文件_一级条标题',
            level: '2',
          },
        ];

        for (var i = 0; i < oDocument.GetElementsCount(); i++) {
          var element = oDocument.GetElement(i);
          if (element.GetClassType() === 'paragraph') {
            const paraPr = element.GetParaPr();
            var pStyle = paraPr.GetStyle();
            if (!pStyle) {
              continue;
            }

            // const level = paraPr.GetOutlineLvl();
            var level = undefined;
            levels.forEach(item => {
              if (item.name == pStyle.GetName()) {
                level = item.level;
              }
            });

            const text = element.GetText();
            // console.log(text,pStyle.GetName());

            // 找到目标标题
            if (!targetHeadingFound && text.trim() === targetHeadingText) {
              targetHeadingFound = true;
              targetHeadingLevel = level;

              continue;
            }
            // 遇到同级或更高级标题时停止收集
            if (targetHeadingFound && level <= targetHeadingLevel) {
              insertPosition = i; // 记录插入位置
              break;
            }
            // 如果已找到目标标题且到达文档末尾
            if (targetHeadingFound && i === oDocument.GetElementsCount() - 1) {
              insertPosition = oDocument.GetElementsCount() - 1; // 在文档末尾插入
            }
          }
        }
        if (!targetHeadingFound) {
          // Api.Notification(`未找到标题: ${targetHeadingText}`, "warning");
          // return;
        }

        if (insertPosition === -1) {
          // Api.Notification("无法确定插入位置", "error");
          // return;
        }

        // console.log(insertPosition);
        // console.log(targetHeadingFound);

        const nParagraph = Api.CreateParagraph();
        nParagraph.AddText('');
        nParagraph.SetStyle('标准文件_前言、引言标题');
        nParagraph.AddPageBreak();
        oDocument.AddElement(insertPosition, oParagraph);
        oDocument.AddElement(insertPosition + 1, nParagraph);
        // oDocument.InsertContent([oParagraph],false);
      },
      false,
      true,
      returnValue => {
        if (returnValue === '-1') {
          this.executeMethod('ShowError', ['文档中已存在标题『引言』，无需重复插入！']);
        }
        this.executeMethod("EndAction", ["Block", "Save to local storage...", ""]);
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    setForewordHeading.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
