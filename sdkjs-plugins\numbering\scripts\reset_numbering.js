(function (window, undefined) {
  window.Asc.plugin.init = function () {
    // 重置编号插件初始化
    console.log('重置编号插件已初始化');

    // 自动执行重置编号功能
    this.resetNumbering();
  };

  // 重置编号功能 - 相当于"开始新列表"
  window.Asc.plugin.resetNumbering = function () {
    console.log('=== 开始执行重置编号功能 ===');

    this.callCommand(function () {
      try {

        var oDocument = Api.GetDocument();
        if (!oDocument) {
          console.error('无法获取文档对象');
          return -1;
        }

        // 获取当前段落位置的辅助函数（在callCommand内部定义）
        function getCurrentParagraphPosition() {
          var currentSentence = oDocument.GetCurrentSentence() || '';

          if (!currentSentence) {
            console.log('未找到当前句子，使用第一个段落');
            return 0;
          }

          var searchStr = '^$' + currentSentence;
          oDocument.ReplaceCurrentSentence(searchStr);

          var targetPosition = -1;
          var allParagraphs = oDocument.GetAllParagraphs();

          for (var i = 0; i < allParagraphs.length; i++) {
            var oParagraph = allParagraphs[i];
            var oText = oParagraph.GetText().trim();
            if (oText.includes(searchStr.trim())) {
              targetPosition = i;
              oDocument.ReplaceCurrentSentence(currentSentence);
              break;
            }
          }

          if (targetPosition === -1) {
            console.log('未找到匹配段落，使用第一个段落');
            oDocument.ReplaceCurrentSentence(currentSentence);
            return 0;
          }

          return targetPosition;
        }

        // 获取当前段落位置
        var currentPosition = getCurrentParagraphPosition();
        var allParagraphs = oDocument.GetAllParagraphs();

        if (currentPosition >= allParagraphs.length) {
          return -1;
        }

        var oParagraph = allParagraphs[currentPosition];
        if (!oParagraph) {
          console.error('无法获取当前段落');
          return -1;
        }

        var numberingType = 'numbered'; // 默认编号类型
        var numberingLevel = 0; // 默认编号级别

        // 获取当前段落的编号信息
        var oNumberingLevel = oParagraph.GetNumbering();
        console.log('当前段落编号信息:', oNumberingLevel);

        numberingLevel = oNumberingLevel.ec

        if (oNumberingLevel) {
          // 如果当前段落有编号，尝试获取编号类型
          console.log('当前段落已有编号，分析编号类型');

          // 获取编号的格式信息
          var oNumbering = oNumberingLevel.GetNumbering();
          console.log('获取编号的格式信息:', oNumbering);

          if (oNumbering) {
            console.log('获取到编号对象，开始分析编号类型');

            // 使用GetClassType方法获取编号类型信息
            try {
              var levelClassType = oNumberingLevel.GetClassType();
              console.log('编号级别类型:', levelClassType);

              var numberingClassType = oNumbering.GetClassType();
              console.log('编号对象类型:', numberingClassType);

              // 根据GetClassType的结果判断编号类型
              // 这里需要根据实际返回值来判断，先保持默认值观察输出
              console.log('使用默认编号类型:', numberingType);

            } catch (e) {
              console.log('获取编号类型信息时出错，使用默认类型:', e.message);
            }

            // 创建新的编号，使用检测到的类型
            var oNewNumbering = oDocument.CreateNumbering(numberingType);
            console.log('创建新编号对象，类型:', numberingType);
            var oNewLevel = oNewNumbering.GetLevel(numberingLevel);
            console.log('获取到新编号级别:', oNewLevel);
            

            // 设置重新开始编号
            oNewLevel.SetRestart(true);
            console.log('设置编号重新开始');

            oParagraph.SetNumbering(oNewLevel);
            console.log('应用新编号到当前段落');
          } else {
            console.log('无法获取编号对象，创建新编号');
            var oNewNumbering = oDocument.CreateNumbering(numberingType);
            var oNewLevel = oNewNumbering.GetLevel(0);
            oParagraph.SetNumbering(oNewLevel);
          }
        } else {
          // 如果当前段落没有编号，应用默认的编号格式
          console.log('当前段落无编号，应用默认编号格式');
          var oNumbering = oDocument.CreateNumbering(numberingType);
          var oLevel = oNumbering.GetLevel(0);
          oParagraph.SetNumbering(oLevel);
          console.log('成功应用默认编号');
        }

        console.log('=== 重置编号操作完成 ===');
        return 1; // 成功状态
      } catch (error) {
        console.error('重置编号过程中发生错误:', error);
        console.error('错误堆栈:', error.stack);
        return -1; // 错误状态
      }
    }, true);

    this.executeMethod('ShowNotification', ['重置编号已应用']);
    this.executeMethod('ClosePlugin');
  };

  // 插件按钮点击事件
  window.Asc.plugin.button = function (id) {
    console.log('插件按钮被点击，按钮ID:', id);

    if (id == 0) {
      // 主按钮被点击，执行重置编号
      console.log('执行重置编号按钮点击事件');
      this.resetNumbering();
    } else {
      console.log('未知按钮ID:', id);
    }
  };
})(window, undefined);
