(function (window, undefined) {
  function resetNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() == 'numberingLevel') {
            console.log('当前段落编号信息:', oNumberingLevel);

            // 获取当前编号的层级
            var currentLevel = oNumberingLevel.GetLevelIndex();
            console.log('当前编号层级:', currentLevel);

            // 获取编号的格式信息
            var oNumbering = oNumberingLevel.GetNumbering();
            console.log('获取编号的格式信息:', oNumbering);

            // 尝试通过GetClassType获取更多信息
            try {
              var levelClassType = oNumberingLevel.GetClassType();
              var numberingClassType = oNumbering.GetClassType();
              console.log('编号级别类型:', levelClassType);
              console.log('编号对象类型:', numberingClassType);
            } catch (e) {
              console.log('获取类型信息失败:', e.message);
            }

            // 尝试两种编号类型：numbered 和 bulleted
            console.log('尝试不同的编号类型');

            // 先尝试 numbered 类型
            try {
              console.log('尝试 numbered 类型');
              var oNewNumbering1 = oDocument.CreateNumbering('numbered');
              var oNewLevel1 = oNewNumbering1.GetLevel(0);
              oParagraph.SetNumbering(oNewLevel1);
              console.log('应用 numbered 类型成功');
            } catch (e) {
              console.log('numbered 类型失败:', e.message);

              // 如果 numbered 失败，尝试 bulleted 类型
              try {
                console.log('尝试 bulleted 类型');
                var oNewNumbering2 = oDocument.CreateNumbering('bulleted');
                var oNewLevel2 = oNewNumbering2.GetLevel(0);
                oParagraph.SetNumbering(oNewLevel2);
                console.log('应用 bulleted 类型成功');
              } catch (e2) {
                console.log('bulleted 类型也失败:', e2.message);
                // 回退到原编号
                oParagraph.SetNumbering(oNumberingLevel);
                console.log('回退到原编号');
              }
            }
            return 1; // 成功状态
          } else {
            console.log('当前段落无Numbering');
            return -1; // 无编号状态
          }
        } catch (error) {
          console.error('重置编号过程中发生错误:', error);
        }
      },
      false,
      true,
      returnValue => {
        if (returnValue == -1) {
          this.executeMethod('ShowError', ['当前段落无编号信息！']);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    resetNumbering.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
