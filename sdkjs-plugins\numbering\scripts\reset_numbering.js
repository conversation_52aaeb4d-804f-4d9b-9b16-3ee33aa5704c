(function (window, undefined) {
  function resetNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() == 'numberingLevel') {
            console.log('当前段落编号信息:', oNumberingLevel);

            // 获取当前编号的层级
            var currentLevel = oNumberingLevel.GetLevelIndex();
            console.log('当前编号层级:', currentLevel);

            // 获取编号的格式信息
            var oNumbering = oNumberingLevel.GetNumbering();
            console.log('获取编号的格式信息:', oNumbering);

            // 尝试不同的重置编号方法
            console.log('尝试重置编号');

            // 方法1: 尝试使用SetRestart方法
            try {
              oNumberingLevel.SetRestart(true);
              console.log('SetRestart(true)调用成功');
            } catch (e) {
              console.log('SetRestart(true)调用失败:', e.message);
            }

            // 方法2: 尝试使用SetRestart方法，参数为false然后true
            try {
              oNumberingLevel.SetRestart(false);
              oNumberingLevel.SetRestart(true);
              console.log('SetRestart重置调用成功');
            } catch (e) {
              console.log('SetRestart重置调用失败:', e.message);
            }

            // 方法3: 尝试获取编号对象的级别并重置
            try {
              var oNumbering = oNumberingLevel.GetNumbering();
              var oLevel = oNumbering.GetLevel(currentLevel);
              oLevel.SetRestart(true);
              oParagraph.SetNumbering(oLevel);
              console.log('通过编号对象重置成功');
            } catch (e) {
              console.log('通过编号对象重置失败:', e.message);
              // 如果上述方法都失败，重新应用当前编号级别
              oParagraph.SetNumbering(oNumberingLevel);
            }
            console.log('应用新编号到当前段落');
            return 1; // 成功状态
          } else {
            console.log('当前段落无Numbering');
            return -1; // 无编号状态
          }
        } catch (error) {
          console.error('重置编号过程中发生错误:', error);
        }
      },
      false,
      true,
      returnValue => {
        if (returnValue == -1) {
          console.log(123);
          this.executeMethod('ShowError', ['当前段落无编号信息！']);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    resetNumbering.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
