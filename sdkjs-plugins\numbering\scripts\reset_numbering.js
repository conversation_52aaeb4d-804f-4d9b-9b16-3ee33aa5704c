(function (window, undefined) {
  function resetNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();

              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();
          console.log('当前段落编号信息:', oNumberingLevel);

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() == 'numberingLevel') {
            console.log('=== 当前段落有编号，开始重置编号 ===');

            // 获取当前编号的层级
            var currentLevel = oNumberingLevel.GetLevelIndex();
            console.log('当前编号层级:', currentLevel);

            // 检测编号类型
            var paragraphText = oParagraph.GetText();
            var numberingType = 'numbered'; // 默认

            if (paragraphText) {
              var firstChar = paragraphText.trim().charAt(0);
              if (firstChar === '•' || firstChar === '○' || firstChar === '■' ||
                  firstChar === '▪' || firstChar === '▫' || firstChar === '◦') {
                numberingType = 'bulleted';
                console.log('检测到项目符号类型');
              } else {
                console.log('检测到数字编号类型');
              }
            }

            console.log('使用编号类型:', numberingType, '层级:', currentLevel);

            // 创建新的编号对象实现重置编号
            var oNewNumbering = oDocument.CreateNumbering(numberingType);
            var oNewLevel = oNewNumbering.GetLevel(currentLevel);

            // 应用新编号到当前段落（这将重置编号从1开始）
            oParagraph.SetNumbering(oNewLevel);
            console.log('成功重置编号，类型:', numberingType, '层级:', currentLevel);

            // 重新编号后续相同层级的段落
            console.log('=== 开始重新编号后续段落 ===');
            for (var j = currentPosition + 1; j < allParagraphs.length; j++) {
              var nextPara = allParagraphs[j];
              var nextNumbering = nextPara.GetNumbering();

              if (nextNumbering && nextNumbering.GetClassType() === 'numberingLevel') {
                var nextLevel = nextNumbering.GetLevelIndex();

                // 如果遇到更高层级的编号，停止查找
                if (nextLevel < currentLevel) {
                  console.log('遇到更高层级编号，停止重新编号，位置:', j, '层级:', nextLevel);
                  break;
                }

                // 处理相同层级或更低层级的段落
                if (nextLevel >= currentLevel) {
                  // 为后续段落应用新编号对象
                  var oSubNewLevel = oNewNumbering.GetLevel(nextLevel);
                  nextPara.SetNumbering(oSubNewLevel);
                  console.log('重新编号段落', j, '层级:', nextLevel);
                }
              }
            }

            console.log('=== 重置编号完成 ===');
            return 1;
          } else {
            console.log('=== 当前段落无编号信息 ===');
            return -1;
          }
        } catch (error) {
          console.error('发生错误:', error);
        }
      },
      false,
      true,
      returnValue => {
        if (returnValue == -1) {
          this.executeMethod('ShowError', ['当前段落无编号信息！']);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    resetNumbering.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
