(function (window, undefined) {
  function resetNumbering() {
    this.callCommand(
      function () {
        try {
          var oDocument = Api.GetDocument();
          if (!oDocument) {
            console.error('无法获取文档对象');
            return -1;
          }

          function getCurrentParagraphPosition() {
            var currentSentence = oDocument.GetCurrentSentence() || '';
            console.log('当前句子内容:', currentSentence);

            if (!currentSentence) {
              console.log('未找到当前句子，使用第一个段落');
              return 0;
            }

            var searchStr = '^$' + currentSentence;
            oDocument.ReplaceCurrentSentence(searchStr);

            var targetPosition = -1;
            var allParagraphs = oDocument.GetAllParagraphs();

            for (var i = 0; i < allParagraphs.length; i++) {
              var oParagraph = allParagraphs[i];
              var oText = oParagraph.GetText().trim();

              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }

            if (targetPosition === -1) {
              console.log('未找到匹配段落，使用第一个段落');
              oDocument.ReplaceCurrentSentence(currentSentence);
              return 0;
            }

            return targetPosition;
          }

          // 获取当前段落位置
          var currentPosition = getCurrentParagraphPosition();
          var allParagraphs = oDocument.GetAllParagraphs();

          if (currentPosition >= allParagraphs.length) {
            return -1;
          }

          var oParagraph = allParagraphs[currentPosition];
          if (!oParagraph) {
            console.error('无法获取当前段落');
            return -1;
          }

          // 获取当前段落的编号信息
          var oNumberingLevel = oParagraph.GetNumbering();

          if (oNumberingLevel && oNumberingLevel.GetClassType() && oNumberingLevel.GetClassType() == 'numberingLevel') {
            var currentLevel = oNumberingLevel.GetLevelIndex();

            var previousNumberingLevel = null;

            var specifiedParagraph = allParagraphs[currentPosition - 1];
            var specifiedNumbering = specifiedParagraph.GetNumbering();
            if (specifiedNumbering && specifiedNumbering.GetClassType() === 'numberingLevel') {
              var specifiedLevel = specifiedNumbering.GetLevelIndex();
              if (specifiedLevel == currentLevel) previousNumberingLevel = specifiedNumbering;
            }

            if (previousNumberingLevel) {
              for (var j = currentPosition - 2; j > 0; j--) {
                var prevParagraph = allParagraphs[j];
                var prevNumbering = prevParagraph.GetNumbering();

                console.log('获取之前段落');
                console.log('    段落内容：', prevParagraph.GetText());
                console.log('    段落信息：', prevNumbering);
                if (prevNumbering && prevNumbering.GetClassType() != 'numberingLevel') {
                  break;
                }
                if (prevNumbering && prevNumbering.GetClassType() === 'numberingLevel') {
                  var prevLevel = prevNumbering.GetLevelIndex();

                  console.log('获取编号索引');
                  console.log('   当前索引：', currentLevel);
                  console.log('之前编号索引：', prevLevel);

                  if (prevLevel == currentLevel - 1) {
                    console.log('检测到父级编号索引');

                    var prevNumberingObj = prevNumbering.GetNumbering();
                    console.log('获取父级编号信息', prevNumberingObj);

                    console.log('标号索引', specifiedLevel);

                    var newPrevNumberingObj = prevNumberingObj.GetLevel(specifiedLevel);
                    console.log('获取父级编号对象', newPrevNumberingObj);

                    newPrevNumberingObj.SetRestart(false)

                    oParagraph.SetNumbering(newPrevNumberingObj);
                    console.log('设置编号');
                    break;
                  }
                }
              }
            }
            return 1;
          } else {
            return -1;
          }
        } catch (error) {
          console.error('发生错误:', error);
        }
      },
      false,
      true,
      returnValue => {
        if (returnValue == -1) {
          this.executeMethod('ShowError', ['当前段落无编号信息！']);
        }
        this.executeCommand('close', '');
      }
    );
  }

  window.Asc.plugin.init = function () {
    resetNumbering.call(this);
  };

  window.Asc.plugin.button = function (id) {};
})(window, undefined);
