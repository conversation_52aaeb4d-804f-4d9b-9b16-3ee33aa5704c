(function(window, undefined) {

    window.Asc.plugin.init = function() {
        // 重置编号插件初始化
        console.log("重置编号插件已初始化");
        console.log("插件版本: 1.0.0");
        console.log("功能: 根据当前编号类型重新开始编号");

        // 自动执行重置编号功能
        this.resetNumbering();
    };

    // 重置编号功能 - 相当于"开始新列表"
    window.Asc.plugin.resetNumbering = function() {
        console.log("=== 开始执行重置编号功能 ===");

        this.callCommand(function() {
            try {
                console.log("进入 callCommand 回调函数");

                var oDocument = Api.GetDocument();
                if (!oDocument) {
                    console.error("无法获取文档对象");
                    return -1;
                }
                console.log("成功获取文档对象");

                // 获取当前段落位置的辅助函数（在callCommand内部定义）
                function getCurrentParagraphPosition() {
                    console.log("开始获取当前段落位置");

                    var currentSentence = oDocument.GetCurrentSentence() || '';
                    console.log("当前句子内容:", currentSentence);

                    if (!currentSentence) {
                        console.log("未找到当前句子，使用第一个段落");
                        return 0;
                    }

                    var searchStr = '^$' + currentSentence;
                    oDocument.ReplaceCurrentSentence(searchStr);

                    var targetPosition = -1;
                    var allParagraphs = oDocument.GetAllParagraphs();
                    console.log("文档总段落数:", allParagraphs.length);

                    for (var i = 0; i < allParagraphs.length; i++) {
                        var oParagraph = allParagraphs[i];
                        var oText = oParagraph.GetText().trim();
                        if (oText.includes(searchStr.trim())) {
                            targetPosition = i;
                            console.log("找到当前段落位置:", targetPosition);
                            oDocument.ReplaceCurrentSentence(currentSentence);
                            break;
                        }
                    }

                    if (targetPosition === -1) {
                        console.log("未找到匹配段落，使用第一个段落");
                        oDocument.ReplaceCurrentSentence(currentSentence);
                        return 0;
                    }

                    return targetPosition;
                }

                // 获取当前段落位置
                var currentPosition = getCurrentParagraphPosition();
                var allParagraphs = oDocument.GetAllParagraphs();

                if (currentPosition >= allParagraphs.length) {
                    console.error("段落位置超出范围:", currentPosition, "总段落数:", allParagraphs.length);
                    return -1;
                }

                var oParagraph = allParagraphs[currentPosition];
                if (!oParagraph) {
                    console.error("无法获取当前段落");
                    return -1;
                }
                console.log("成功获取当前段落，位置:", currentPosition);

                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();
                console.log("当前段落编号信息:", oNumberingLevel ? "有编号" : "无编号");

                var numberingType = "numbered"; // 默认编号类型

                if (oNumberingLevel) {
                    // 如果当前段落有编号，尝试获取编号类型
                    console.log("当前段落已有编号，分析编号类型");

                    // 获取编号的格式信息
                    var oNumbering = oNumberingLevel.GetNumbering();
                    if (oNumbering) {
                        console.log("获取到编号对象，开始分析编号类型");

                        // 尝试获取编号类型
                        try {
                            // 检查编号格式来判断类型
                            var levelText = oNumberingLevel.GetLevelText();
                            console.log("编号级别文本:", levelText);

                            if (levelText) {
                                // 根据格式判断编号类型
                                if (levelText.includes("•") || levelText.includes("○") || levelText.includes("■") || levelText.includes("▪")) {
                                    numberingType = "bulleted";
                                    console.log("检测到项目符号类型，符号:", levelText);
                                } else if (levelText.includes("%1") || /\d/.test(levelText) || levelText.includes(".") || levelText.includes(")")) {
                                    numberingType = "numbered";
                                    console.log("检测到数字编号类型，格式:", levelText);
                                }
                            }

                            // 尝试获取编号格式代码
                            try {
                                var numFormat = oNumberingLevel.GetNumberingFormat();
                                console.log("编号格式代码:", numFormat);

                                // 根据格式代码判断类型
                                if (numFormat === 0 || numFormat === "decimal" || numFormat === 1 || numFormat === "upperRoman" || numFormat === 2 || numFormat === "lowerRoman") {
                                    numberingType = "numbered";
                                    console.log("通过格式代码检测到数字编号，代码:", numFormat);
                                } else if (numFormat === 23 || numFormat === "bullet") {
                                    numberingType = "bulleted";
                                    console.log("通过格式代码检测到项目符号，代码:", numFormat);
                                }
                            } catch (formatError) {
                                console.log("无法获取编号格式代码:", formatError.message);
                            }

                        } catch (e) {
                            console.log("无法获取编号格式详情，使用默认类型:", e.message);
                        }

                        console.log("最终确定的编号类型:", numberingType);

                        // 创建新的编号，使用检测到的类型
                        var oNewNumbering = oDocument.CreateNumbering(numberingType);
                        console.log("创建新编号对象，类型:", numberingType);
                        var oNewLevel = oNewNumbering.GetLevel(0);

                        // 设置重新开始编号
                        try {
                            oNewLevel.SetRestart(1);
                            console.log("设置编号重新开始为1");
                        } catch (restartError) {
                            console.log("SetRestart方法调用失败:", restartError.message);
                            // 尝试其他方法重置编号
                            try {
                                oNewLevel.SetStart(1);
                                console.log("使用SetStart方法设置起始编号为1");
                            } catch (startError) {
                                console.log("SetStart方法也失败:", startError.message);
                            }
                        }

                        oParagraph.SetNumbering(oNewLevel);
                        console.log("应用新编号到当前段落，类型:", numberingType);
                    } else {
                        console.log("无法获取编号对象，创建默认编号");
                        var oNewNumbering = oDocument.CreateNumbering(numberingType);
                        var oNewLevel = oNewNumbering.GetLevel(0);
                        oParagraph.SetNumbering(oNewLevel);
                        console.log("应用默认编号，类型:", numberingType);
                    }
                } else {
                    // 如果当前段落没有编号，应用默认的编号格式
                    console.log("当前段落无编号，应用默认编号格式");
                    var oNumbering = oDocument.CreateNumbering(numberingType);
                    var oLevel = oNumbering.GetLevel(0);
                    oParagraph.SetNumbering(oLevel);
                    console.log("成功应用默认编号，类型:", numberingType);
                }

                console.log("=== 重置编号操作完成 ===");
                return 1; // 成功状态

            } catch (error) {
                console.error("重置编号过程中发生错误:", error);
                console.error("错误堆栈:", error.stack);
                return -1; // 错误状态
            }
        }, true);

        // 显示成功消息并关闭插件
        console.log("准备显示通知和关闭插件");
        this.executeMethod('ShowNotification', ['重置编号已应用']);
        this.executeMethod('ClosePlugin');
    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        console.log("插件按钮被点击，按钮ID:", id);

        if (id == 0) {
            // 主按钮被点击，执行重置编号
            console.log("执行重置编号按钮点击事件");
            this.resetNumbering();
        } else {
            console.log("未知按钮ID:", id);
        }
    };

})(window, undefined);
