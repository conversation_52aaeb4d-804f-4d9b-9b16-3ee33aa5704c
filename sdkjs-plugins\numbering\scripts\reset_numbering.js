(function(window, undefined) {
    
    window.Asc.plugin.init = function() {
        // 重置编号插件初始化
        console.log("重置编号插件已初始化");
        
        // 自动执行重置编号功能
        this.resetNumbering();
    };

    // 重置编号功能 - 相当于"开始新列表"
    window.Asc.plugin.resetNumbering = function() {
        console.log("执行重置编号");
        
        this.callCommand(function() {
            var oDocument = Api.GetDocument();
            var oParagraph = oDocument.GetCurrentParagraph();
            
            if (oParagraph) {
                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();
                
                if (oNumberingLevel) {
                    // 如果当前段落有编号，创建新的编号并重新开始
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    var oNewLevel = oNumbering.GetLevel(0);
                    oParagraph.SetNumbering(oNewLevel);
                } else {
                    // 如果当前段落没有编号，应用默认的编号格式
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    var oLevel = oNumbering.GetLevel(0);
                    oParagraph.SetNumbering(oLevel);
                }
            }
        }, true);
        
        // 显示成功消息并关闭插件
        this.executeMethod('ShowNotification', ['重置编号已应用']);
        this.executeMethod('ClosePlugin');
    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            // 主按钮被点击，执行重置编号
            this.resetNumbering();
        }
    };

})(window, undefined);
